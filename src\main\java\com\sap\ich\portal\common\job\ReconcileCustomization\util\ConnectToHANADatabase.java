package com.sap.ich.portal.common.job.ReconcileCustomization.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

import static com.sap.ich.portal.common.job.ReconcileCustomization.MessageKeys.HANA_DB_CONNECTION_ERROR;

public class ConnectToHANADatabase {

    private static final Logger log = LoggerFactory.getLogger(ConnectToHANADatabase.class);

    public static Connection connectToHANADatabase(String HANA_URL, String HANA_USER, String HANA_PASSWORD){
        Connection connection;
        Properties info = new Properties();
        try {
            info.put("user", HANA_USER);
            info.put("password", HANA_PASSWORD);
            connection = DriverManager.getConnection(HANA_URL, info);
        }
        catch (SQLException e) {
            log.error("Connection to HANA Failed:" + e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, HANA_DB_CONNECTION_ERROR);
        }
        return connection;
    }
}
