package com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "ICH_MANAGE_CUSTOMIZATION_PARAMETERVALUE")
@SuppressWarnings("unused")
public class ParameterValue implements Serializable{
    
    static final long serialVersionUID = 1L;

    @Column(name = "PARAMETEROPTION_PARAMETEROPTIONID")
    private Integer parameterOptionId;

    @Id
    @Column(name = "PARAMETERVALUEID")
    private Integer parameterValueId;

    @Column(name = "PARAMETERVALUE")
    private String parameterValue;
}
