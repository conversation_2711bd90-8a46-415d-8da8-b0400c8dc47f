package com.sap.ich.portal.common.job.ReconcileCustomization.controller;

import com.sap.ich.portal.common.job.ReconcileCustomization.ReconcileCustomizationApplication;
import com.sap.ich.portal.common.job.ReconcileCustomization.service.IflowService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = ReconcileCustomizationApplication.class)
@AutoConfigureMockMvc
class SyncReceiveNotificationControllerTest {

    @MockBean
    IflowService iflowService;

    @Autowired
    private MockMvc mockMvc;

    /*@Test
    public void syncRecvNotificationTest() throws Exception {
        try (MockedStatic<DriverManager> mockedDriverManager = mockStatic(DriverManager.class)) {
            Connection mockConnection = mock(Connection.class);
            Statement statement = mock(Statement.class);
            ResultSet resultSet = mock(ResultSet.class);

            mockedDriverManager.when(() -> DriverManager.getConnection(anyString(), any()))
                    .thenReturn(mockConnection);
            when(mockConnection.createStatement()).thenReturn(statement);
            when(statement.executeQuery(anyString())).thenReturn(resultSet);
            when(resultSet.next()).thenReturn(true).thenReturn(false);
            when(resultSet.getString("PNID")).thenReturn("pnid");
            when(resultSet.getString("MULTIPLEPARAMETERVALUESSTRING")).thenReturn("pnid1,pnid2");

            mockMvc.perform(post("/syncrecvnotification.svc/api/v1/syncerecvnotification"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("completed"));

            mockedDriverManager.verify(() -> DriverManager.getConnection(anyString(), any()), times(1));
        }

    }*/

}