server.error.include-message=always
hana.url = ${vcap.services.por-db-common-ManageCustomization.credentials.url}
hana.user = ${vcap.services.por-db-common-ManageCustomization.credentials.user}
hana.password = ${vcap.services.por-db-common-ManageCustomization.credentials.password}

hana.por-organization-mgmt-db.url = ${vcap.services.por-organization-mgmt-db.credentials.url}
hana.por-organization-mgmt-db.user = ${vcap.services.por-organization-mgmt-db.credentials.user}
hana.por-organization-mgmt-db.password = ${vcap.services.por-organization-mgmt-db.credentials.password}

#For connecting to Customization Management db
hana.por-db-common-ManageCustomization.url = ${vcap.services.por-db-common-ManageCustomization.credentials.url}
hana.por-db-common-ManageCustomization.user = ${vcap.services.por-db-common-ManageCustomization.credentials.user}
hana.por-db-common-ManageCustomization.password = ${vcap.services.por-db-common-ManageCustomization.credentials.password}


management.endpoints.access.default=none
management.endpoint.health.access=read-only
management.health.defaults.enabled=false
management.endpoint.health.show-components=never
management.endpoint.health.show-details=never
management.endpoint.health.probes.enabled=false
management.endpoint.health.probes.add-additional-paths=false
management.endpoints.access.default=none
management.endpoint.health.access=read-only
management.health.defaults.enabled=false
management.endpoint.health.show-components=never
management.endpoint.health.show-details=never
management.endpoint.health.probes.enabled=false
management.endpoint.health.probes.add-additional-paths=false