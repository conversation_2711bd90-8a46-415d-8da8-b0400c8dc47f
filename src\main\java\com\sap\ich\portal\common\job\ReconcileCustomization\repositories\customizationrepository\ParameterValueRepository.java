package com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository;

import org.springframework.data.jpa.repository.JpaRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterValue;

public interface ParameterValueRepository extends JpaRepository<ParameterValue, String> {
 
ParameterValue findTopByParameterValueIdAndParameterOptionId(Integer paramValueId, Integer parameterOptionId);
 
}