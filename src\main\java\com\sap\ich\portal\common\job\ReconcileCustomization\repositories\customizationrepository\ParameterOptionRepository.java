package com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterOption;
 
public interface ParameterOptionRepository extends JpaRepository<ParameterOption, String> {
 
    List<ParameterOption> findAllBycpiStringParameterNotNull();

    ParameterOption findTopByCpiStringParameter(String cpiStringParameter);
}