package com.sap.ich.portal.common.job.ReconcileCustomization.configuration;


import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository",
        entityManagerFactoryRef = "customizationEntityManagerFactory",
        transactionManagerRef = "customizationTransactionManager")
public class CustomizationEntityManagerFactoryConfig {

    @Bean(name = "customizationEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean customizationEntityManagerFactory(
            @Qualifier("customizationdbDataSource") DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        // Set the entity package for this instance
        
        em.setPackagesToScan("com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization");
        em.setPersistenceProviderClass(org.hibernate.jpa.HibernatePersistenceProvider.class);

        Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.dialect", "org.hibernate.dialect.HANAColumnStoreDialect");
        hibernateProperties.setProperty("jpa.hibernate.ddl-auto", "none");
        hibernateProperties.setProperty("hibernate.format_sql", "true");
        hibernateProperties.setProperty("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        em.setJpaProperties(hibernateProperties);
        // Set other JPA properties if required

        return em;
    }

    // Transaction manager for each entity manager
    @Bean(name = "customizationTransactionManager")
    public PlatformTransactionManager customizationTransactionManager(
            @Qualifier("customizationEntityManagerFactory") EntityManagerFactory customizationEntityManagerFactory) {
        return new JpaTransactionManager(customizationEntityManagerFactory);
    }
}
