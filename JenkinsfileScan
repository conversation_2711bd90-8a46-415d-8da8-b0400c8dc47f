@Library(['piper-lib', 'piper-lib-os']) _
node {
    try {
        stage('Preparation') {
            deleteDir()
            def params = checkout scm
            setupPipelineEnvironment(script: this, scmInfo: params)
        }
 
        def branchName = env.<PERSON><PERSON><PERSON>_NAME
        def blackduckProjectName = ""
        def sonarProjectKey = ""
        def sonarProjectName = ""
        if (branchName == 'develop') {
            blackduckProjectName = "SHC-REGCOLLABORATION"
            sonarProjectKey = "SAP_ICH-portal-common-job-reconciliation-customization"
            sonarProjectName = "portal-common-job-reconciliation-customization"
        } else if (branchName == 'staging') {
            blackduckProjectName = "SHC-REGCOLLABORATION-Staging"
            sonarProjectKey = "Staging-ICH_portal-common-job-Reconciliation-Customization"
            sonarProjectName = "Staging_portal-common-job-Reconciliation-Customization"
        } else if (branchName == 'production') {
            blackduckProjectName = "SHC-REGCOLLABORATION-Production"
            sonarProjectKey = "Production-ICH_portal-common-job-Reconciliation-Customization"
            sonarProjectName = "Production_portal-common-job-Reconciliation-Customization"
        }

 
        // BlackDuck Scan
        stage('BlackDuck Scan') {
            detectExecuteScan(
                script: this,
                detectTokenCredentialsId: "regcol2-blackduck-token",
                buildTool: "maven",
                projectName: blackduckProjectName,
                version: "portal-common-job-Reconciliation-Customization",
                versioningModel: "full",
                codeLocation: "${blackduckProjectName}/portal-common-job-Reconciliation-Customization",
                minScanInterval: 0,
                groups: ["REGCOLLABPORTALOD10"],
                failOn: ["NONE"],
                useDetect9: true,
                npmDependencyTypesExcluded: ["DEV"],
                scanProperties: [
                    '--detect.detector.search.depth=2'
                    '--detect.project.version.distribution=SAAS'
                    '--detect.maven.build.command=compile'
                    '--detect.risk.report.pdf=true'
                    '--detect.impact.analysis.enabled=true'
                    '--detect.blackduck.signature.scanner.paths = target/'
                    '--blackduck.signature.scanner.memory=4096'
                    '--detect.timeout=6000'
                    '--blackduck.trust.cert=true'
                    '--logging.level.com.synopsys.integration=DEBUG'
                    '--detect.maven.excluded.scopes=test'
                    '--detect.project.codelocation.unmap=true'
                ]
            )
        }
 
        // SonarQube Scan
        stage('SonarQube Scan') {
            sonarExecuteScan(
                script: this,
                sonarTokenCredentialsId: 'ICH_Sonar_I061035',
                serverUrl: 'https://sonar.tools.sap',
                projectKey : sonarProjectKey,
                projectName: sonarProjectName
              )
        }
        
 
        stage('Post-Scan Actions') {
            // Upload or archive reports, send notifications, etc.
            echo 'All security scans completed successfully.'
        }
    } catch (Exception e) {
        echo "Pipeline failed: ${e.message}"
        currentBuild.result = 'FAILURE'
        throw e
    }
}
 
 