package com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository;

import org.springframework.data.jpa.repository.JpaRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterValueUItoPDMapping;

public interface ParameterValueUItoPDMappingRepository extends JpaRepository<ParameterValueUItoPDMapping, String> {

    ParameterValueUItoPDMapping findTopByParameterValueUI(String parameterValueUI);
}
