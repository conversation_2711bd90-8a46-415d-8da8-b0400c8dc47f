general:
  buildTool: "maven"
  productiveBranch: "release-.*|hotfix-.*"
  gitHttpsCredentialsId: "ICH_tkn_GitHub"
  vaultAppRoleTokenCredentialsId: "vault-approle-role-id-2614-22313"
  vaultAppRoleSecretTokenCredentialsId: "vault-approle-secret-id-accessor-2614-22313"
  vaultPath: "piper/PIPELINE-GROUP-2614"
  vaultBasePath: "piper/PIPELINE-GROUP-2614"
  vaultPipelineName: "PIPELINE-22313"
  vaultServerUrl: "https://vault.tools.sap"
  vaultNamespace: "ies/hyperspace/pipelines"
  verbose: true
stages:
  Central Build:
    hadolintExecute: false
  Acceptance:
    verbose: true
    cfApiEndpoint: https://api.cf.eu10-004.hana.ondemand.com
    cfOrg: ich-qa-new-org
    cfSpace: portal
    cfManifest: manifest-qa.yaml
    cfManifestVariablesFiles:
      - vars-qa.yml
    cfCredentialsId: "ICH_pwd_Cloud_Platform"
    mtaDeployParameters: -f -version-rule ALL
    testServerUrl: www.google.com
  Confirm:
    manualConfirmation: false
    manualConfirmationTimeout: 0.001
steps:
  downloadArtifactsFromNexus:
    disableLegacyNaming: true
  artifactPrepareVersion:
    gitHttpsCredentialVaultSecretName: "GROUP-SECRETS/github"
    versioningType: "library"
  mtaBuild:
    mtaBuildTool: cloudMbt
  executeBuild:
    xMakeNovaCredentialsId: "hyperspace-xmake-p2003178105"
  sonarExecuteScan:
    serverUrl: "https://sonar.tools.sap"
    sonarTokenCredentialsId: "hyperspace-sonar-c5182688"
    sonarVaultSecretName: "GROUP-SECRETS/sonar"
  sapCumulusUpload:
    pipelineId: "7e4fe1ae-6e74-455d-b2c8-b1178fc6f9bb"
    cumulusFileCredentialsId: "hyperspace-cumulusupload-2614"
 