#!/usr/bin/env groovy
@Library(['piper-lib', 'piper-lib-os']) _
try {
    
    stage('Central Build') {
        lock(resource: "${env.JOB_NAME}/10", inversePrecedence: true) {
            milestone 10
            node {
                deleteDir()
                checkout scm
                setupPipelineEnvironment script: this
                durationMeasure (script: this, measurementName: 'build_duration') {          
                }
            }
        }
    }

    stage('Deploy to Staging') {
        lock(resource: "${env.JOB_NAME}/10", inversePrecedence: true) {
            milestone 20
            node {
                deleteDir()
                durationMeasure(script: this, measurementName: 'deploy_staging_duration') {
                    checkout scm
                    downloadArtifactsFromNexus script: this, disableLegacyNaming: true , nexusUrl: 'https://int.repositories.cloud.sap/', promoteEndpoint : 'artifactory/', promoteRepository: 'build-milestones/', fromStaging: false, xMakeBuildQuality:'Milestone', artifactType: 'maven', buildTool: 'maven', verbose: true
                    cloudFoundryDeploy script: this, cfOrg: 'ich-dev-org', cfSpace: 'portal', cfManifest: 'manifest-dev.yaml', deployTool: 'cf_native', testServerUrl: 'www.google.com', cfCredentialsId: 'ICH_pwd_Cloud_Platform', cfApiEndpoint: 'https://api.cf.eu12.hana.ondemand.com'

                }
            }
        }
    }

} catch (Throwable err) { // catch all exceptions
    globalPipelineEnvironment.addError(this, err)
    throw err
} finally {
    node{
        mailSendNotification script: this
        cleanWs()
    }
}
