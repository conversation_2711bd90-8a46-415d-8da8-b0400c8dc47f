import org.jenkinsci.plugins.pipeline.modeldefinition.Utils
@Library(['piper-lib','piper_pscdp']) _

environment {
  DEPLOY_TO = 'QA'
}

def take_user_input(){
  def userInput 
  milestone()
  timeout(unit: 'HOURS',time: 1){
    currentBuild.description = "Waiting for user to select an env to deploy!"
    userInput = input(
        id: 'userInput',
        message: 'Choose an env to deploy',
        parameters: [[$class: 'ChoiceParameterDefinition', 
          description:'All available environments', name:'Choice of Env', choices: "QA\nDevelop\nDev Validation\nQA Validation"]
        ]
    ) 
  }
  return userInput
}

def get_deploy_env() {
  if (params.DEPLOY_TO) {
    return params.DEPLOY_TO
  } else {
    return take_user_input()
  }
}

try {  
  if (env.BRANCH_NAME.equals('hyperspace') || env.BRANCH_NAME.equals('develop')) {
      env.DEPLOY_TO = get_deploy_env()
      currentBuild.description = "${env.DEPLOY_TO} env selected!"
      sapPiperPipeline script: this, customDefaults: ['configdevelop_redirect.yml']
  }
  else if (env.BRANCH_NAME.startsWith('release') || env.BRANCH_NAME.startsWith('hotfix')) {
    sapPiperPipeline script: this, customDefaults: ['configrel_redirect.yml']
  }
      else if (env.BRANCH_NAME.startsWith('PR')) {
    sapPiperPipeline script: this, customDefaults: ['configPR_redirect.yml']
  }
} 
catch (Throwable err) { // catch all exceptions
  globalPipelineEnvironment.addError(this, err)
  throw err
} 
finally {
  node{
    // mailSendNotification script: this
    cleanWs()
  }
}
