package com.sap.ich.portal.common.job.ReconcileCustomization.controller;

import com.sap.ich.portal.common.job.ReconcileCustomization.ReconcileCustomizationApplication;
import com.sap.ich.portal.common.job.ReconcileCustomization.service.IflowService;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.entity.StringEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.sql.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = ReconcileCustomizationApplication.class)
@AutoConfigureMockMvc
class ReconcileCustomizationControllerTest {

    @MockBean
    IflowService iflowService;

    @Autowired
    private MockMvc mockMvc;

    /*@Test
    public void customizationReconcileTest1() throws Exception {

        try (MockedStatic<DriverManager> mockedDriverManager = mockStatic(DriverManager.class)) {

            Connection mockConnectionCommon = mock(Connection.class);
            Connection mockConnectionOrg = mock(Connection.class);
            Statement statement1 = mock(Statement.class);
            Statement statement = mock(Statement.class);
            Statement statementCPIParam = mock(Statement.class);
            Statement statementCustomization = mock(Statement.class);
            Statement statementOrg = mock(Statement.class);
            ResultSet resultSet1 = mock(ResultSet.class);
            ResultSet resultSet = mock(ResultSet.class);
            ResultSet resultSet3 = mock(ResultSet.class);
            ResultSet resultSetCpStringParam = mock(ResultSet.class);
            ResultSet resultSetCustomization = mock(ResultSet.class);
            ResultSet resultSetParameterOption = mock(ResultSet.class);
            ResultSet resultPartnerOrgs = mock(ResultSet.class);

            when(DriverManager.getConnection(anyString(), any()))
                    .thenAnswer(invocation -> {
                        String url = invocation.getArgument(0);

                        if ("******************************".equals(url)) {
                            return mockConnectionOrg;
                        } else if ("*********************************".equals(url)) {
                            return mockConnectionCommon;
                        } else {
                            throw new SQLException("Invalid arguments");
                        }
                    });
            when(mockConnectionCommon.createStatement()).thenReturn(statement1).thenReturn(statement).thenReturn(statementCPIParam).thenReturn(statementCustomization);
            when(statement1.executeQuery(anyString())).thenReturn(resultSet1);
            when(resultSet1.next()).thenReturn(true).thenReturn(false);
            when(resultSet1.getString("PNID")).thenReturn("pnid");

            when(mockConnectionOrg.createStatement()).thenReturn(statementOrg);
            when(statementOrg.executeQuery(anyString())).thenReturn(resultPartnerOrgs);
            when(resultPartnerOrgs.next()).thenReturn(true).thenReturn(false);
            when(resultPartnerOrgs.getString("ID")).thenReturn("pnid");

            when(statement.executeQuery(anyString())).thenReturn(resultSet).thenReturn(resultSet3);
            when(resultSet.next()).thenReturn(true).thenReturn(false);
            when(resultSet.getString("PARAMETERVALUE")).thenReturn("1024 Days");
            when(resultSet.getString("PARAMETERVALUETEXT")).thenReturn("1024 Days");
            when(resultSet.getString("ALIASNAME")).thenReturn("1024 Days");
            when(resultSet.getString("CPISTRINGPARAMETER")).thenReturn("SP_PARAM");
            when(resultSet.getString("SYNCTO")).thenReturn("eu");

            when(resultSet3.next()).thenReturn(true).thenReturn(false);
            when(resultSet3.getString("PARAMETERVALUEPD")).thenReturn("1024 days");

            when(statementCPIParam.executeQuery(anyString())).thenReturn(resultSetCpStringParam);
            when(resultSetCpStringParam.next()).thenReturn(true).thenReturn(false);
            when(resultSetCpStringParam.getString("NAME")).thenReturn("IS_CF_MIGRATED");

            when(statementCustomization.executeQuery(anyString())).thenReturn(resultSetCustomization).thenReturn(resultSetParameterOption);
            when(resultSetCustomization.next()).thenReturn(false);
            when(resultSetParameterOption.next()).thenReturn(true);
            when(resultSetParameterOption.getString("SYNCTO")).thenReturn("us");

            HttpResponse mockResponse = mock(HttpResponse.class);
            StatusLine mockedStatusLine = mock(StatusLine.class);
            when(mockedStatusLine.getStatusCode()).thenReturn(500).thenReturn(404).thenReturn(200).thenReturn(200).thenReturn(200);

            when(iflowService.callIflow(any(), anyString())).thenReturn(mockResponse);
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);

            when(iflowService.getStringParameters(anyString(),anyString(),anyString(),anyString())).thenReturn(mockResponse);
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);
            when(mockResponse.getEntity()).thenReturn(new StringEntity(
                 "<?xml version='1.0' encoding='utf-8'?>\n" +
                         "<feed\n" +
                         "\txmlns=\"http://www.w3.org/2005/Atom\"\n" +
                         "\txmlns:m=\"http://schemas.microsoft.com/ado/2007/08/dataservices/metadata\"\n" +
                         "\txmlns:d=\"http://schemas.microsoft.com/ado/2007/08/dataservices\" xml:base=\"https://ich-net-qa.it-cpi001.cfapps.eu10.hana.ondemand.com:443/api/v1/\">\n" +
                         "\t<id>https://ich-net-qa.it-cpi001.cfapps.eu10.hana.ondemand.com:443/api/v1/StringParameters</id>\n" +
                         "\t<title type=\"text\">StringParameters</title>\n" +
                         "\t<updated>2024-05-03T09:56:21.737Z</updated>\n" +
                         "\t<author>\n" +
                         "\t\t<name/>\n" +
                         "\t</author>\n" +
                         "\t<link href=\"StringParameters\" rel=\"self\" title=\"StringParameters\"/>\n" +
                         "\t<entry>\n" +
                         "\t\t<id>https://ich-net-qa.it-cpi001.cfapps.eu10.hana.ondemand.com:443/api/v1/StringParameters(Pid='NOVA_CMO_PORTAL',Id='IS_CF_MIGRATED')</id>\n" +
                         "\t\t<title type=\"text\">StringParameters</title>\n" +
                         "\t\t<updated>2024-05-03T09:56:21.738Z</updated>\n" +
                         "\t\t<category term=\"com.sap.hci.api.StringParameter\" scheme=\"http://schemas.microsoft.com/ado/2007/08/dataservices/scheme\"/>\n" +
                         "\t\t<link href=\"StringParameters(Pid='NOVA_CMO_PORTAL',Id='IS_CF_MIGRATED')\" rel=\"edit\" title=\"StringParameter\"/>\n" +
                         "\t\t<content type=\"application/xml\">\n" +
                         "\t\t\t<m:properties>\n" +
                         "\t\t\t\t<d:Pid>NOVA_CMO_PORTAL</d:Pid>\n" +
                         "\t\t\t\t<d:Id>IS_CF_MIGRATED</d:Id>\n" +
                         "\t\t\t\t<d:LastModifiedBy>sb-36472bc5-f74f-4df7-9926-4bdaccf47222!b76937|it!b16077</d:LastModifiedBy>\n" +
                         "\t\t\t\t<d:LastModifiedTime>2022-11-22T06:28:54.787</d:LastModifiedTime>\n" +
                         "\t\t\t\t<d:CreatedBy>sb-36472bc5-f74f-4df7-9926-4bdaccf47222!b76937|it!b16077</d:CreatedBy>\n" +
                         "\t\t\t\t<d:CreatedTime>2022-11-22T06:28:54.787</d:CreatedTime>\n" +
                         "\t\t\t\t<d:Value>true</d:Value>\n" +
                         "\t\t\t</m:properties>\n" +
                         "\t\t</content>\n" +
                         "\t</entry>\n" +
                         "\t<entry>\n" +
                         "\t\t<id>https://ich-net-qa.it-cpi001.cfapps.eu10.hana.ondemand.com:443/api/v1/StringParameters(Pid='PNSAP_4001',Id='EUHUB_ALERT_CREDENTIALS')</id>\n" +
                         "\t\t<title type=\"text\">StringParameters</title>\n" +
                         "\t\t<updated>2024-05-03T09:56:21.738Z</updated>\n" +
                         "\t\t<category term=\"com.sap.hci.api.StringParameter\" scheme=\"http://schemas.microsoft.com/ado/2007/08/dataservices/scheme\"/>\n" +
                         "\t\t<link href=\"StringParameters(Pid='PNSAP_4001',Id='EUHUB_ALERT_CREDENTIALS')\" rel=\"edit\" title=\"StringParameter\"/>\n" +
                         "\t\t<content type=\"application/xml\">\n" +
                         "\t\t\t<m:properties>\n" +
                         "\t\t\t\t<d:Pid>PNSAP_4001</d:Pid>\n" +
                         "\t\t\t\t<d:Id>EUHUB_ALERT_CREDENTIALS</d:Id>\n" +
                         "\t\t\t\t<d:LastModifiedBy>sb-36472bc5-f74f-4df7-9926-4bdaccf47222!b76937|it!b16077</d:LastModifiedBy>\n" +
                         "\t\t\t\t<d:LastModifiedTime>2023-03-23T08:55:13.858</d:LastModifiedTime>\n" +
                         "\t\t\t\t<d:CreatedBy>sb-36472bc5-f74f-4df7-9926-4bdaccf47222!b76937|it!b16077</d:CreatedBy>\n" +
                         "\t\t\t\t<d:CreatedTime>2021-09-21T06:10:44.108</d:CreatedTime>\n" +
                         "\t\t\t\t<d:Value>ATTP_Y21</d:Value>\n" +
                         "\t\t\t</m:properties>\n" +
                         "\t\t</content>\n" +
                         "\t</entry>\n" +
                         "</feed>"

            ));


            when(iflowService.postStringParameters(anyString(),anyString(),any())).thenReturn(mockResponse);
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);

            when(iflowService.deleteStringParameters(anyString(),anyString(),anyString(),anyString())).thenReturn(mockResponse);

            mockMvc.perform(get("/CustomizationReconcileJob"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("Job completed"));

            //mockedDriverManager.verify(() -> DriverManager.getConnection(anyString(), any()), times(2));

        }
    }

    @Test
    public void customizationReconcileTest2() throws Exception {

        try (MockedStatic<DriverManager> mockedDriverManager = mockStatic(DriverManager.class)) {

            Connection mockConnectionCommon = mock(Connection.class);
            Connection mockConnectionOrg = mock(Connection.class);
            Statement statement1 = mock(Statement.class);
            Statement statement = mock(Statement.class);
            Statement statementOrg = mock(Statement.class);
            ResultSet resultSet1 = mock(ResultSet.class);
            ResultSet resultSet = mock(ResultSet.class);
            ResultSet resultSet3 = mock(ResultSet.class);
            ResultSet resultPartnerOrgs = mock(ResultSet.class);

            when(DriverManager.getConnection(anyString(), any()))
                    .thenAnswer(invocation -> {
                        String url = invocation.getArgument(0);

                        if ("******************************".equals(url)) {
                            return mockConnectionOrg;
                        } else if ("*********************************".equals(url)) {
                            return mockConnectionCommon;
                        } else {
                            throw new SQLException("Invalid arguments");
                        }
                    });
            when(mockConnectionCommon.createStatement()).thenReturn(statement1).thenReturn(statement);
            when(statement1.executeQuery(anyString())).thenReturn(resultSet1);
            when(resultSet1.next()).thenReturn(true).thenReturn(false);
            when(resultSet1.getString("PNID")).thenReturn("pnid");

            when(mockConnectionOrg.createStatement()).thenReturn(statementOrg);
            when(statementOrg.executeQuery(anyString())).thenReturn(resultPartnerOrgs);
            when(resultPartnerOrgs.next()).thenReturn(true).thenReturn(false);
            when(resultPartnerOrgs.getString("ID")).thenReturn("PNNOVARTIS20160811");

            HttpResponse mockResponse = mock(HttpResponse.class);
            StatusLine mockedStatusLine = mock(StatusLine.class);
            when(mockedStatusLine.getStatusCode()).thenReturn(200).thenReturn(404).thenReturn(200);
            when(iflowService.callIflow(any(), anyString())).thenReturn(mockResponse);
            when(mockResponse.getEntity()).thenReturn(new StringEntity(
                    """
                            {
                            	"root": [
                            		{
                            			"Pid": "PNNOVARTIS20160811",
                            			"Id": "EPCIS_EXTENSION_VALUE",
                            			"Value": "ILMD1.0"
                            		},
                            		{
                            			"Pid": "PNNOVARTIS20160811",
                            			"Id": "GR_BASIC_CREDENTIAL",
                            			"Value": "PNNOVARTIS20160811_GR_Basic"
                            		},
                            		{
                                      			"Pid": "PNNOVARTIS20160811",
                                      			"Id": "ICH_TITS_ERROR_RETENTION",
                                      			"Value": "30Days"
                                    }
                                ]
                            }
                    """
            ));
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);

            when(statement.executeQuery(anyString())).thenReturn(resultSet).thenReturn(resultSet3);
            when(resultSet.next()).thenReturn(true).thenReturn(false);
            when(resultSet.getString("PARAMETERVALUE")).thenReturn("1024 Days");
            when(resultSet.getString("PARAMETERVALUETEXT")).thenReturn("1024 Days");
            when(resultSet.getString("ALIASNAME")).thenReturn("1024 Days");
            //when(resultSet.getString("CPISTRINGPARAMETER")).thenReturn("SP_PARAM");
            when(resultSet.getString("SYNCTO")).thenReturn("eu");

            when(resultSet3.next()).thenReturn(true).thenReturn(false);
            when(resultSet3.getString("PARAMETERVALUEPD")).thenReturn("1024 days");

            when(iflowService.getStringParameters(anyString(),anyString(),anyString(),anyString())).thenReturn(mockResponse);
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);

            when(iflowService.postStringParameters(anyString(),anyString(),any())).thenReturn(mockResponse);
            when(mockResponse.getStatusLine()).thenReturn(mockedStatusLine);

            mockMvc.perform(get("/CustomizationReconcileJob"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("Job completed"));

            //mockedDriverManager.verify(() -> DriverManager.getConnection(anyString(), any()), times(2));

        }
    }*/
}