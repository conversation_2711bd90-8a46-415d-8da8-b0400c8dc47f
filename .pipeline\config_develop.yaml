general:
  buildTool: "maven"
  productiveBranch: "hyperspace|develop"
  gitHttpsCredentialsId: "ICH_tkn_GitHub"
  vaultAppRoleTokenCredentialsId: "vault-approle-role-id-2614-22313"
  vaultAppRoleSecretTokenCredentialsId: "vault-approle-secret-id-accessor-2614-22313"
  vaultPath: "piper/PIPELINE-GROUP-2614"
  vaultBasePath: "piper/PIPELINE-GROUP-2614"
  vaultPipelineName: "PIPELINE-22313"
  vaultServerUrl: "https://vault.tools.sap"
  vaultNamespace: "ies/hyperspace/pipelines"
  verbose: true
stages:
  Central Build:
    hadolintExecute: false
  Acceptance:
    verbose: true
    cfApiEndpoint: https://api.cf.eu10-004.hana.ondemand.com
    cfOrg: ich-qa-new-org
    cfSpace: portal
    cfManifest: manifest-qa.yaml
    cfManifestVariablesFiles:
      - vars-qa.yml
    cfCredentialsId: "ICH_pwd_Cloud_Platform"
    mtaDeployParameters: -f -version-rule ALL
    testServerUrl: www.google.com
  Security:
    detectExecuteScan: true
  Confirm:
    manualConfirmation: false
    manualConfirmationTimeout: 0.001
steps:
  downloadArtifactsFromNexus:
    disableLegacyNaming: true
  influxWriteData:
    influxServer: jenkins
  artifactPrepareVersion:
    gitHttpsCredentialVaultSecretName: "GROUP-SECRETS/github"
    versioningType: "library"
  mtaBuild:
    mtaBuildTool: cloudMbt
  executeBuild:
    xMakeNovaCredentialsId: "hyperspace-xmake-p2003178105"
  sonarExecuteScan: 
    sonarTokenCredentialsId: 'ICH_tkn_Sonar'
    serverUrl: 'https://sonar.tools.sap'
    #host: 'https://sonar.wdf.sap.corp'
    verbose: false  
    instance: ""
    projectKey: 'SAP_ICH-portal-common-job-reconciliation-customization'
    projectName: 'portal-common-job-reconciliation-customization'
  sapCumulusUpload:
    pipelineId: "7e4fe1ae-6e74-455d-b2c8-b1178fc6f9bb"
    cumulusFileCredentialsId: "hyperspace-cumulusupload-2614"
  detectExecuteScan:
    dockerImage: 'maven:3.6.3-jdk-11'
    projectName: "SHC-REGCOLLABORATION"
    version: "portal-common-job-Reconciliation-Customization"
    versioningModel: 'full'
    codeLocation: "SHC-REGCOLLABORATION/portal-common-job-Reconciliation-Customization"
    minScanInterval: 0
    groups:
      - "REGCOLLABPORTALOD10"
    failOn:
      - NONE
    detectTokenCredentialsId: "regcol2-blackduck-token"
    useDetect8: true
    npmDependencyTypesExcluded:
      - DEV
    scanProperties:
      - '--detect.detector.search.depth=2'
      - '--detect.project.version.distribution=SAAS'
      - '--detect.maven.build.command=compile'
      - '--detect.risk.report.pdf=true'
      - '--detect.impact.analysis.enabled=true'
      - '--detect.blackduck.signature.scanner.paths = target/'
      - '--blackduck.signature.scanner.memory=4096'
      - '--detect.timeout=6000'
      - '--blackduck.trust.cert=true'
      - '--logging.level.com.synopsys.integration=DEBUG'
      - '--detect.maven.excluded.scopes=test'
      - '--detect.project.codelocation.unmap=true'
