# Job for managing customizations in ICH

This is a spring boot application which contains 2 APIs which are configured as recurring jobs in jobscheduler.

## Prerequisites

Make sure you have installed all the following prerequisites on your development machine:
- Java 17
- Maven 3.8.8

## Clone, Build and Deploy the service

1. <PERSON>lone the repository
   ```bash
   git clone https://github.wdf.sap.corp/SAP-ICH/portal-common-Job-Invitation-Manager.git
   ```
2. Build the project
   ```bash
   mvn clean install
   ```
3. Deploy the project
   ```bash
   cf push -f manifest-dev.yml
   ```

## Jobs

| Sl no | API                                                      | Frequency    | Use Case                                                                                    | Business Scenario     | HDI Container Used                                           |
|-------|----------------------------------------------------------|--------------|---------------------------------------------------------------------------------------------|-----------------------|--------------------------------------------------------------|
| 1     | /CustomizationReconcileJob                               | Every 12 hrs | Override the value changed directly in PD/SP from db for customizations                     | Manage Customizations | por-db-common-ManageCustomization,por-organization-mgmt-db   |
| 2     | /syncrecvnotification.svc/api/v1/syncerecvnotification   | Once a day   | Sync customization for Suppress Aggregation in Receive Notification from db to data store   | Manage Customizations | por-db-common-ManageCustomization,por-organization-mgmt-db   |