general:
  buildTool: "maven"
  productiveBranch: "hyperspace|develop|hotfix-.*"
  gitHttpsCredentialsId: "ICH_tkn_GitHub"
  vaultAppRoleTokenCredentialsId: "vault-approle-role-id-2614-22313"
  vaultAppRoleSecretTokenCredentialsId: "vault-approle-secret-id-accessor-2614-22313"
  vaultPath: "piper/PIPELINE-GROUP-2614"
  vaultBasePath: "piper/PIPELINE-GROUP-2614"
  vaultPipelineName: "PIPELINE-22313"
  vaultServerUrl: "https://vault.tools.sap"
  vaultNamespace: "ies/hyperspace/pipelines"
  verbose: true
stages:
  Pull-Request Voting:
    sonarExecuteScan: true
    npmExecuteLint: true
    dockerBuildImage: "dockerio.int.repositories.cloud.sap/devxci/mbtci-java17-node18"
    dockerBuildCommand: "mvn clean install"
steps:
  downloadArtifactsFromNexus:
    disableLegacyNaming: true
  artifactPrepareVersion:
    gitHttpsCredentialVaultSecretName: "GROUP-SECRETS/github"
    versioningType: "library"
  mtaBuild:
    mtaBuildTool: cloudMbt
  executeBuild:
    xMakeNovaCredentialsId: "hyperspace-xmake-p2003178105"
  sonarExecuteScan: 
    sonarTokenCredentialsId: 'ICH_tkn_Sonar'
    serverUrl: 'https://sonar.tools.sap'
    #host: 'https://sonar.wdf.sap.corp'
    verbose: false
    projectKey: 'SAP_ICH-portal-common-job-reconciliation-customization'
    projectName: 'portal-common-job-reconciliation-customization'  
    instance: ""
  sapCumulusUpload:
    pipelineId: "7e4fe1ae-6e74-455d-b2c8-b1178fc6f9bb"
    cumulusFileCredentialsId: "hyperspace-cumulusupload-2614"
  mtaBuild:
    dockerImage: devxci/mbtci-java17-node18    
  detectExecuteScan:
    dockerImage: 'maven:3.6.3-jdk-11'
    projectName: "SHC-REGCOLLABORATION-JOBS"
    version: "portal-common-job-Reconciliation-Customization"
    versioningModel: 'full'
    codeLocation: "SHC-REGCOLLABORATION-JOBS/portal-common-job-Reconciliation-Customization"
    minScanInterval: 0
    groups:
      - "REGCOLLABPORTALOD10"
    failOn:
      - NONE
    detectTokenCredentialsId: "regcol2-blackduck-token"
    useDetect8: true
    npmDependencyTypesExcluded:
      - DEV
    scanProperties:
      - '--detect.detector.search.depth=2'
      - '--detect.project.version.distribution=SAAS'
      - '--detect.included.detector.types=NPM'
      - '--detect.accuracy.required=NONE'
      - '--detect.risk.report.pdf=true'
      - '--detect.project.codelocation.unmap=true'
