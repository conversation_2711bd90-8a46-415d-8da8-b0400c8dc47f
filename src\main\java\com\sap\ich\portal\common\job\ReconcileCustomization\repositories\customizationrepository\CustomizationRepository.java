package com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.Customization;
 
public interface CustomizationRepository extends JpaRepository<Customization, String> {
 
    Customization findTopByPnidAndParameterOptionId(String pnid, Integer parameterOptionId);
    Customization findTopByPnidAndParameterValueText(String pnid, String parameterValueText);
    List<Customization> findAllByFileNameNotNull();
    
    List<Customization> findAllByparameterOptionId(Integer parameterOptionId);
 
}