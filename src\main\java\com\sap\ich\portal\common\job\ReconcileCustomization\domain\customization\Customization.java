package com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "ICH_MANAGE_CUSTOMIZATION_CUSTOMIZATION")
@SuppressWarnings("unused")
public class Customization implements Serializable {

    static final long serialVersionUID = 1L;

    @Id
    @Column(name = "CUSTOMIZATIONID")
    private String customizationId;

    @Column(name = "PNID")
    private String pnid;

    @Column(name = "SCENARIO_ID")
    private Integer scenarioId;

    @Column(name = "PARAMETERKEY_PARAMETERKEYID")
    private Integer parameterKeyParameterKeyId;

    @Column(name = "PARAMETEROPTION_PARAMETEROPTIONID")
    private Integer parameterOptionId;

    @Column(name = "ALIASNAME")
    private String aliasName;

    @Column(name = "CREDENTIALENCODED")
    private String credentialEncoded;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "PARAMETERVALUETEXT")
    private String parameterValueText;

    @Column(name = "MULTIPLEPARAMETERVALUESSTRING")
    private String multipleParameterValuesString;

    @Column(name = "ISSAPINTERNAL")
    private Boolean isSapInternal;

    @Column(name = "FILECONTENT")
    private byte[] fileContent;

    @Column(name = "FILENAME")
    private String fileName;

    @CreationTimestamp
    @Column(name = "CREATEDAT")
    private Timestamp createdDate;

    @Column(name = "CREATEDBY")
    private String createdBy;
    
    @Column(name = "MODIFIEDAT")
    private Timestamp modifiedAt;

    @Column(name = "MODIFIEDBY")
    private String modifiedBy;

}