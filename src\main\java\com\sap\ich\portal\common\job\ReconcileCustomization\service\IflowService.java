package com.sap.ich.portal.common.job.ReconcileCustomization.service;

import java.io.IOException;

import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.sap.cloud.sdk.cloudplatform.connectivity.DestinationAccessor;
import com.sap.cloud.sdk.cloudplatform.connectivity.HttpClientAccessor;
import com.sap.cloud.sdk.cloudplatform.connectivity.HttpDestination;

@Service
public class IflowService {

    private static final String IFLOW_DESTINATION = "CPI-Tenant-Iflow-API";
    private static HttpClient iflowClient;
    private static HttpDestination iflowDestination;

    private final Logger log = LoggerFactory.getLogger(getClass());

    public HttpResponse callIflow(JSONObject payload, String endpoint) throws IOException {

        iflowDestination = DestinationAccessor.getDestination(IFLOW_DESTINATION).asHttp();
        iflowClient = HttpClientAccessor.getHttpClient(iflowDestination);

        String uri = iflowDestination.getUri().toString();
        uri = uri + endpoint;

        HttpPost post = new HttpPost(uri);
        post.setEntity(new StringEntity(payload.toString()));
        return iflowClient.execute(post);
    }

    public HttpResponse updateStringParameters(String destination, String endpoint, String pid, String id,
            JSONObject payload) throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        HttpClient httpClient = HttpClientAccessor.getHttpClient(httpDestination);

        String uri = httpDestination.getUri().toString();
        uri = uri + endpoint + "(Pid='" + pid + "',Id='" + id + "')";

        HttpPut put = new HttpPut(uri);
        put.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        put.setEntity(new StringEntity(payload.toString()));
        return httpClient.execute(put);
    }

    public HttpResponse getStringParameters(String destination, String endpoint, String pid, String id)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String uri = httpDestination.getUri().toString();
        uri += endpoint + "(Pid='" + pid + "',Id='" + id + "')";

        log.info("SP Url = " + uri);
        HttpGet get = new HttpGet(uri);
        get.addHeader("Authorization", httpDestination.getHeaders().stream().toList().get(0).getValue());
        get.addHeader("Accept", "application/json");
        HttpResponse res = httpClient.execute(get);

        //get.releaseConnection();
        //httpClient.close();

        log.info("Get response = " + res.toString());
        if (res.getStatusLine().getStatusCode() == 200) {
            log.info("Get response entity = " + res.getEntity().toString());
            return res;
        }

        if (res.getStatusLine().getStatusCode() == 404) {
            return null;
        }

        return res;
    }
     
    public HttpResponse getAllPnidsForStringParameters(String destination, String endpoint, String id)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String uri = httpDestination.getUri().toString();
        uri += endpoint + "?$filter=Id%20eq%20'"+ id +"'";

        log.info("SP Urlfor getting all pnids = " + uri);
        HttpGet get = new HttpGet(uri);
        get.addHeader("Authorization", httpDestination.getHeaders().stream().toList().get(0).getValue());
        get.addHeader("Accept", "application/json");
        HttpResponse res = httpClient.execute(get);

        log.info("Get response = " + res.toString());
        if (res.getStatusLine().getStatusCode() == 200) {
            log.info("Get response entity = " + res.getEntity().toString());
            return res;
        }

        if (res.getStatusLine().getStatusCode() == 404) {
            return null;
        }
        
        return res;
    }

    public HttpResponse postStringParameters(String destination, String endpoint, JSONObject payload)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        HttpClient httpClient = HttpClientAccessor.getHttpClient(httpDestination);
        String uri = httpDestination.getUri().toString();
        uri += endpoint;

        HttpPost post = new HttpPost(uri);
        post.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        post.setEntity(new StringEntity(payload.toString()));
        return httpClient.execute(post);
    }

    public HttpResponse deleteStringParameters(String destination, String endpoint, String pid, String id)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        HttpClient httpClient = HttpClientAccessor.getHttpClient(httpDestination);
        String uri = httpDestination.getUri().toString();
        uri = uri + endpoint + "(Pid='" + pid + "',Id='" + id + "')";

        HttpDelete delete = new HttpDelete(uri);
        delete.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        return httpClient.execute(delete);
    }

    public int createEntryInDataStore(String payload) throws IOException {

        iflowDestination = DestinationAccessor.getDestination(IFLOW_DESTINATION).asHttp();
        iflowClient = HttpClientAccessor.getHttpClient(iflowDestination);

        HttpPost post = new HttpPost(iflowDestination.getUri() + "/bhr_suppress_agg");
        post.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        post.setEntity(new StringEntity(payload));

        HttpResponse response = iflowClient.execute(post);

        log.info("SAP Cloud Integration response: {}", response.toString());

        return response.getStatusLine().getStatusCode();

    }

    public HttpResponse getCountStringParameters(String destination, String endpoint)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String uri = httpDestination.getUri().toString();
        uri += endpoint + "?$top="+ 0 + "&$skip="+ 0+ "&$inlinecount=allpages";

        log.info("SP Urlfor getting all count = " + uri);
        HttpGet get = new HttpGet(uri);
        get.addHeader("Accept", "application/json");
        HttpResponse res = httpClient.execute(get);

        log.info("Get response = " + res.toString());
        if (res.getStatusLine().getStatusCode() == 200) {
            log.info("Get response entity = " + res.getEntity().toString());
            return res;
        }

        if (res.getStatusLine().getStatusCode() == 404) {
            return null;
        }

        return res;
    }

    public HttpResponse getAllStringParameters(String destination, String endpoint, int top, int skip)
            throws IOException {
        HttpDestination httpDestination = DestinationAccessor.getDestination(destination).asHttp();
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String uri = httpDestination.getUri().toString();
        uri += endpoint + "?$top="+ top + "&$skip="+ skip + "&$inlinecount=allpages";

        log.info("SP Urlfor getting all params with skip and top = " + uri);
        HttpGet get = new HttpGet(uri);
        get.addHeader("Accept", "application/json");
        HttpResponse res = httpClient.execute(get);

        //log.info("Get response = " + res.toString());
        if (res.getStatusLine().getStatusCode() == 200) {
            //log.info("Get response entity = " + res.getEntity().toString());
            return res;
        }

        if (res.getStatusLine().getStatusCode() == 404) {
            return null;
        }

        return res;
    }

    public boolean emailNotify(String email, ResponseEntity<byte[]> attachment)
            throws IOException {
        iflowDestination = DestinationAccessor.getDestination(IFLOW_DESTINATION).asHttp();
        iflowClient = HttpClientAccessor.getHttpClient(iflowDestination);

        HttpPost post = new HttpPost(iflowDestination.getUri() + "/Reconciliation_Report_Email");
        post.setHeader("Email_Address", email);
        post.setHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        post.setEntity(new ByteArrayEntity(attachment.getBody()));
        
        try (CloseableHttpResponse response = (CloseableHttpResponse)iflowClient.execute(post)) {
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200) {
                log.error("Email sent failed with error code: {}", statusCode);
                return false;
            }
            log.info("Email sent successfully");
        }
        return true;
    }

}
