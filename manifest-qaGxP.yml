---
applications:
  - name: por-job-common-reconcilecustomize
    host: qa-por-job-common-reconcilecustomize
    memory: 1G
    buildpack: sap_java_buildpack
    path: target/ReconcileCustomization-2505.0.0.jar
    env:
      SPRING_PROFILES_ACTIVE: cloud
      JBP_CONFIG_COMPONENTS: "jres: ['com.sap.xs.java.buildpack.jre.SAPMachineJRE']"
      JBP_CONFIG_SAP_MACHINE_JRE: '{ version: 17.+ }'
      Email_Address: ''
    routes:
      - route: por-job-common-reconcilecustomize-((SPACE_NAME))-((ORG_NAME)).((HOST))
    services:
      - por-dest-common-horizontal
      - por-db-common-ManageCustomization
      - por-logging-common-horizontal
      - por-jobscheduler-common-horizontal
      - por-organization-mgmt-db
      - name: por-autoscaler
        parameters:
          instance_min_count: 1
          instance_max_count: 5
          scaling_rules:
            - metric_type: "cpu"
              breach_duration_secs: 60
              cool_down_secs: 60
              threshold: 60
              operator: ">="
              adjustment: "+1"
            - metric_type: "memoryutil"
              breach_duration_secs: 60
              cool_down_secs: 60
              threshold: 70
              operator: ">="
              adjustment: "+1"
            - metric_type: "memoryutil"
              breach_duration_secs: 60
              cool_down_secs: 60
              threshold: 70
              operator: "<"
              adjustment: "-1"
            - metric_type: "cpu"
              breach_duration_secs: 60
              cool_down_secs: 60
              threshold: 60
              operator: "<"
              adjustment: "-1"
