package com.sap.ich.portal.common.job.ReconcileCustomization.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
public class HanaConfig {

    @Primary
    @Bean
    public DataSource customizationdbDataSource(@Value("${hana.por-db-common-ManageCustomization.url}") final String url,
                                             @Value("${hana.por-db-common-ManageCustomization.user}") final String user,
                                             @Value("${hana.por-db-common-ManageCustomization.password}") final String password) {

        return DataSourceBuilder.create()
                .url(url)
                .username(user)
                .password(password)
                .driverClassName("com.sap.db.jdbc.Driver")
                .build();
    }
}