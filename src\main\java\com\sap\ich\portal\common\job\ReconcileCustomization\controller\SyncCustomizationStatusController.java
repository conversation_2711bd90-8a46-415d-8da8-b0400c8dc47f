package com.sap.ich.portal.common.job.ReconcileCustomization.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import com.sap.ich.portal.common.job.ReconcileCustomization.MessageKeys;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.Customization;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterOption;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterValue;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterValueUItoPDMapping;
import com.sap.ich.portal.common.job.ReconcileCustomization.model.ReportDto;
import com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository.CustomizationRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository.ParameterKeyRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository.ParameterOptionRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository.ParameterValueRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository.ParameterValueUItoPDMappingRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.service.IflowService;

import io.vavr.collection.Stream;

@RestController
@RequestMapping("/api")
public class SyncCustomizationStatusController {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final IflowService iflowService;
    HashMap<String, List<String>> binaryParams = new HashMap<>();

    private static final String UNBRACKETED_FORMAT = "Unbracketed Format";
    private static final String DAYS = "Days";
    private static final String ILMD1GS = "ILMD 1.0(gs1ushc)";
    private static final String ILMD2CB = "ILMD 2.0(cbvmda)";
    private static final String THREE = "3";
    private static final String ILMD1 = "ILMD1.0";
    private static final String ILMD2 = "ILMD2.0";
    private static final String CERTIFICATE = "Certificate";
    private static final String CERT = "CERT";
    private static final String ASYNCHRONOUS = "Asynchronous";
    private static final String ASYNC = "ASYNC";
    private static final String SYNCHRONOUS = "Synchronous";
    private static final String SYNC = "SYNC";
    private static final String ATTP = "ATTP";

    private final CustomizationRepository customizationRepository;
    private final ParameterOptionRepository parameterOptionRepository;
    private final ParameterValueRepository parameterValueRepository;
    private final ParameterKeyRepository parameterKeyRepository;
    private final ParameterValueUItoPDMappingRepository parameterValueUItoPDMappingRepository;

    @Value("${Email_Address}")
    private String email;

    public SyncCustomizationStatusController(CustomizationRepository customizationRepository,
            ParameterOptionRepository parameterOptionRepository, ParameterValueRepository parameterValueRepository,
            IflowService iflowService, ParameterKeyRepository parameterKeyRepository,
            ParameterValueUItoPDMappingRepository parameterValueUItoPDMappingRepository) {

        this.customizationRepository = customizationRepository;
        this.parameterOptionRepository = parameterOptionRepository;
        this.parameterValueRepository = parameterValueRepository;
        this.iflowService = iflowService;
        this.parameterKeyRepository = parameterKeyRepository;
        this.parameterValueUItoPDMappingRepository = parameterValueUItoPDMappingRepository;

    }

    @GetMapping("/syncDifference")
    public ResponseEntity<String> viewInvitation() throws SQLException, JSONException, ParseException, IOException {

        List<ParameterOption> po = parameterOptionRepository.findAllBycpiStringParameterNotNull();
        List<String> allStringParametersInDB = new ArrayList<>();

        // logger.info("po = " + po);
        // logger.info("Getting all parameter options");

        for (ParameterOption paramOption : po) {
            allStringParametersInDB.add(paramOption.getCpiStringParameter());
        }

        logger.info("Checking difference - Source of truth is DB");
        List<ReportDto> reportDto = new ArrayList<>();

        ResponseEntity<byte[]> res = getValueDifferenceInSPFromDB(reportDto, po);

        if(iflowService.emailNotify(email, res)) {
            return ResponseEntity.ok().body("Email sent successfully with the report.");
        } else {
            logger.error("Failed to send email with the report.");
            return ResponseEntity.status(HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .body("Failed to send email with the report.");
        }

    }

    private ResponseEntity<byte[]> getValueDifferenceInSPFromDB(
            List<ReportDto> reportDto, List<ParameterOption> po)
            throws JSONException, ParseException, IOException {

        HashMap<String, List<String>> pnidMap = new HashMap<>();

        for (ParameterOption paramOption : po) {
            // logger.info("Starting to check for PO = " +
            // paramOption.getCpiStringParameter());

            int paramOptionIdInCustomization = paramOption.getParameterOptionId();
            List<Customization> customizationsForParameterOptionId = customizationRepository
                    .findAllByparameterOptionId(paramOptionIdInCustomization);

            for (Customization customization : customizationsForParameterOptionId) {

                pnidMap = getPnidProcessedForSP(customization, paramOption.getCpiStringParameter(), pnidMap);

                if (!parameterKeyRepository.findTopByParameterKeyId(customization.getParameterKeyParameterKeyId())
                        .getHasMultiSelectParameterValue()) {
                    String valueForThisParamOption = getValueForParameterOptionAndPnid(customization);
                    // logger.info("Value in DB = " + valueForThisParamOption);

                    valueForThisParamOption = convertPVFromDBtoPDFormat(valueForThisParamOption);
                    String valueInPDForThisParamOptionAndPNID = getValueInPD(customization.getPnid(),
                            paramOption.getCpiStringParameter(), paramOption.getSyncTo());
                    // logger.info("Value in PD = " + valueInPDForThisParamOptionAndPNID);

                    if (!valueForThisParamOption.equalsIgnoreCase(valueInPDForThisParamOptionAndPNID)) {

                        ReportDto reportDtoObj = buildReportDto(paramOption.getCpiStringParameter(),
                                customization.getPnid(), valueForThisParamOption, valueInPDForThisParamOptionAndPNID,
                                "db", paramOption.getSyncTo());
                        reportDto.add(reportDtoObj);
                    }
                } else {
                    // logger.info("Comparing customization having multiple values");
                    List<String> valueForThisParamOptionWithMultiselectInDB = getListOfValuesInDB(customization);
                    // logger.info("Value in DB for multi select = " +
                    // valueForThisParamOptionWithMultiselectInDB);

                    List<String> valueInPDForThisParamOptionAndPNIDWithMultiselect = getValueInPDWithMultiSelect(
                            customization.getPnid(),
                            paramOption.getCpiStringParameter(), paramOption.getSyncTo());
                    // logger.info("Value in PD for multi select = " +
                    // valueInPDForThisParamOptionAndPNIDWithMultiselect);

                    if ((valueInPDForThisParamOptionAndPNIDWithMultiselect == null)
                            || !(valueForThisParamOptionWithMultiselectInDB
                                    .equals(valueInPDForThisParamOptionAndPNIDWithMultiselect))) {

                        String valueInPD = null;
                        if (valueInPDForThisParamOptionAndPNIDWithMultiselect != null) {
                            valueInPD = String.join(",", valueInPDForThisParamOptionAndPNIDWithMultiselect);
                        }

                        ReportDto repDTO = buildReportDto(paramOption.getCpiStringParameter(), customization.getPnid(),
                                String.join(",", valueForThisParamOptionWithMultiselectInDB),
                                valueInPD, "db", paramOption.getSyncTo());
                        reportDto.add(repDTO);
                    }
                }
            }
        }

        reportDto = getValueOnlyInPDNotInDB(reportDto, po, pnidMap);

        reportDto = getValueOnlyInPDNotAvailableInCustomization(reportDto, po, "eu");

        reportDto = getValueOnlyInPDNotAvailableInCustomization(reportDto, po, "us");

        reportDto = getValueOnlyInBinaryParamNotInCustomizationDB(reportDto, "eu");

        reportDto = getValueOnlyInBinaryParamNotInCustomizationDB(reportDto, "us");

        reportDto = getValueOnlyInDBNotInBP(reportDto, binaryParams);

        return generateExcelFile(reportDto);
    }

    private List<ReportDto> getValueOnlyInDBNotInBP(List<ReportDto> reportDto,
            HashMap<String, List<String>> binaryParamsList) {

        List<Customization> customizations = customizationRepository.findAllByFileNameNotNull();
        logger.info("customizations count = " + customizations.size());

        for (Customization customization : customizations) {
            String tenant = customization.getParameterKeyParameterKeyId() == 961 ? "us" : "eu";
            if (binaryParamsList.containsKey(customization.getPnid())) {
                List<String> spIDs = binaryParamsList.get(customization.getPnid());

                if (!spIDs.contains(customization.getParameterValueText())) {

                    ReportDto repDTO = buildReportDto(customization.getParameterValueText().toUpperCase(),
                            customization.getPnid(),
                            "Binary Parameter Not Available in PD",
                            "file", "db", tenant);
                    reportDto.add(repDTO);
                }
            } else {
                ReportDto repDTO = buildReportDto(customization.getParameterValueText().toUpperCase(),
                        customization.getPnid(),
                        "Binary Parameter Not Available in PD",
                        "file", "db", tenant);
                reportDto.add(repDTO);
            }
        }
        return reportDto;
    }

    private List<ReportDto> getValueOnlyInBinaryParamNotInCustomizationDB(List<ReportDto> reportDto, String dest)
            throws IOException {

        JSONObject responseObject;
        JSONArray currentPDValues;

        String destination = (dest.equalsIgnoreCase("eu")) ? "CPI-Tenant-API" : "VRS_CPI-Tenant-API";
        try(CloseableHttpResponse getResponse = (CloseableHttpResponse)iflowService.getCountStringParameters(destination,
                "/BinaryParameters")) {

            if (getResponse != null && getResponse.getStatusLine().getStatusCode() == 200) {
                responseObject = new JSONObject(EntityUtils.toString(getResponse.getEntity()));
                if (responseObject.has("d")) {
                    JSONObject jsonObj = (JSONObject) responseObject.get("d");
                    Integer countSP = Integer.valueOf(jsonObj.get("__count").toString());
                    logger.info("CURRENT count for BP = " + countSP);
                    countSP = (countSP % 30 == 0) ? (countSP / 300) : ((countSP / 30) + 1);
                    logger.info("counter loop times = " + countSP);

                    for (int loop = 0; loop < countSP; loop++) {
                        int top = 30;
                        int skip = loop * 30;
                        try(CloseableHttpResponse getResponseFromSP = (CloseableHttpResponse)iflowService.getAllStringParameters(destination,
                                "/BinaryParameters", top, skip)) {

                            if (getResponseFromSP != null && getResponseFromSP.getStatusLine().getStatusCode() == 200) {
                                responseObject = new JSONObject(EntityUtils.toString(getResponseFromSP.getEntity()));
                                // logger.info("responseObject = " + responseObject);
                                if (responseObject.has("d")) {
                                    JSONObject jsonObjpnid = (JSONObject) responseObject.get("d");
                                    currentPDValues = (JSONArray) jsonObjpnid.get("results");
                                    // logger.info("CURRENT PD Values = " + currentPDValues);

                                    int length = currentPDValues.length();
                                    logger.info("length = " + length);
                                    for (int len = 0; len < length; len++) {
                                        JSONObject obj = currentPDValues.getJSONObject(len);
                                        String pnidJson = obj.get("Pid").toString();
                                        String spID = obj.get("Id").toString();

                                        logger.info("PID = " + pnidJson + " sp id " + spID);
                                        if (binaryParams.containsKey(pnidJson)) {
                                            List<String> listOfParamIds = binaryParams.get(pnidJson);
                                            listOfParamIds.add(spID);
                                            binaryParams.put(pnidJson, listOfParamIds);
                                        } else {
                                            List<String> listOfParamIds = new ArrayList<>();
                                            listOfParamIds.add(spID);
                                            binaryParams.put(pnidJson, listOfParamIds);
                                        }

                                        Customization customization = customizationRepository
                                                .findTopByPnidAndParameterValueText(pnidJson, spID);
                                        logger.info("customization = " + customization);

                                        if (customization == null) {
                                            logger.info("DB Does not contain BP = " + spID);

                                            ReportDto repDTO = buildReportDto(spID.toUpperCase(), pnidJson,
                                                    "Binary Parameter Not Available in DB",
                                                    "file", "BP", dest);
                                            reportDto.add(repDTO);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return reportDto;
    }

    private List<ReportDto> getValueOnlyInPDNotAvailableInCustomization(List<ReportDto> reportDto,
            List<ParameterOption> po, String dest) throws IOException {

        JSONObject responseObject;
        JSONArray currentPDValues;
        List<String> allStringParametersInDB = new ArrayList<>();

        for (ParameterOption paramOption : po) {
            allStringParametersInDB.add(paramOption.getCpiStringParameter().toLowerCase());
        }
        String destination = (dest.equalsIgnoreCase("eu")) ? "CPI-Tenant-API" : "VRS_CPI-Tenant-API";
        try(CloseableHttpResponse getResponse = (CloseableHttpResponse)iflowService.getCountStringParameters(destination,
                "/StringParameters")) {

            if (getResponse != null && getResponse.getStatusLine().getStatusCode() == 200) {
                responseObject = new JSONObject(EntityUtils.toString(getResponse.getEntity()));
                if (responseObject.has("d")) {
                    JSONObject jsonObj = (JSONObject) responseObject.get("d");
                    Integer countSP = Integer.valueOf(jsonObj.get("__count").toString());
                    logger.info("CURRENT count = " + countSP);
                    countSP = (countSP % 500 == 0) ? (countSP / 500) : ((countSP / 500) + 1);
                    logger.info("counter loop times = " + countSP);

                    for (int loop = 0; loop < countSP; loop++) {
                        int top = 500;
                        int skip = loop * 500;
                        try(CloseableHttpResponse getResponseFromSP = (CloseableHttpResponse)iflowService.getAllStringParameters(destination,
                                "/StringParameters", top, skip)) {

                            if (getResponseFromSP != null && getResponseFromSP.getStatusLine().getStatusCode() == 200) {
                                responseObject = new JSONObject(EntityUtils.toString(getResponseFromSP.getEntity()));
                                logger.info("responseObject = " + responseObject);
                                if (responseObject.has("d")) {
                                    JSONObject jsonObjpnid = (JSONObject) responseObject.get("d");
                                    currentPDValues = (JSONArray) jsonObjpnid.get("results");
                                    // logger.info("CURRENT PD Values = " + currentPDValues);

                                    int length = currentPDValues.length();
                                    for (int len = 0; len < length; len++) {
                                        JSONObject obj = currentPDValues.getJSONObject(len);
                                        String pnidJson = obj.get("Pid").toString();
                                        String spID = obj.get("Id").toString().toLowerCase();
                                        String val = obj.get("Value").toString();

                                        // logger.info("PID = " + pnidJson + " sp id "+ spID);
                                        if (!allStringParametersInDB.contains(spID)) {
                                            logger.info("DB Does not contain SP = " + spID);

                                            ReportDto repDTO = buildReportDto(spID.toUpperCase(), pnidJson,
                                                    "Parameter Not Available in DB",
                                                    val, "PD", destination);
                                            reportDto.add(repDTO);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return reportDto;
    }

    private List<ReportDto> getValueOnlyInPDNotInDB(List<ReportDto> reportDto, List<ParameterOption> po,
            HashMap<String, List<String>> pnidMap) throws IOException {

        // logger.info("PNID MAP = " + pnidMap);
        JSONObject responseObject;
        JSONArray currentPDValues;

        for (ParameterOption parameterOption : po) {

            String destination = (parameterOption.getSyncTo().equals("eu")) ? "CPI-Tenant-API" : "VRS_CPI-Tenant-API";
            try(CloseableHttpResponse getResponse = (CloseableHttpResponse)iflowService.getAllPnidsForStringParameters(destination,
                    "/StringParameters",
                    parameterOption.getCpiStringParameter())) {

                if (getResponse != null && getResponse.getStatusLine().getStatusCode() == 200) {
                    responseObject = new JSONObject(EntityUtils.toString(getResponse.getEntity()));
                    logger.info("responseObject = " + responseObject);
                    if (responseObject.has("d")) {
                        JSONObject jsonObj = (JSONObject) responseObject.get("d");
                        currentPDValues = (JSONArray) jsonObj.get("results");
                        logger.info("CURRENT PD Values = " + currentPDValues);

                        List<String> alreadyProcessed = new ArrayList<>();
                        if (pnidMap.containsKey(parameterOption.getCpiStringParameter())) {
                            alreadyProcessed = pnidMap.get(parameterOption.getCpiStringParameter());
                        }
                        int length = currentPDValues.length();
                        for (int len = 0; len < length; len++) {
                            JSONObject obj = currentPDValues.getJSONObject(len);
                            String pnidJson = obj.get("Pid").toString();
                            logger.info("PID = " + pnidJson);
                            if (!alreadyProcessed.contains(pnidJson)) {
                                ReportDto repDTO = buildReportDto(parameterOption.getCpiStringParameter(), pnidJson,
                                        "Does not Exist",
                                        obj.getString("Value"), "PD", parameterOption.getSyncTo());
                                reportDto.add(repDTO);
                            }
                        }
                    }
                }
            }
        }
        return reportDto;
    }

    private HashMap<String, List<String>> getPnidProcessedForSP(Customization customization, String cpiStringParameter,
            HashMap<String, List<String>> stringParamToPnidMapping) {

        // HashMap<String, List<String>> stringParamToPnidMapping = new HashMap<>();

        if (!stringParamToPnidMapping.containsKey(cpiStringParameter)) {
            String spID = cpiStringParameter;
            List<String> pnidList = new ArrayList<>();
            pnidList.add(customization.getPnid());
            stringParamToPnidMapping.put(spID, pnidList);
        } else {
            List<String> existingPnidList = stringParamToPnidMapping.get(cpiStringParameter);
            existingPnidList.add(customization.getPnid());
            stringParamToPnidMapping.put(cpiStringParameter, existingPnidList);
        }

        return stringParamToPnidMapping;
    }

    public ResponseEntity<byte[]> generateExcelFile(List<ReportDto> reportDto) {
        try (Workbook workbook = new XSSFWorkbook()) {
            // Create a sheet
            Sheet sheet = workbook.createSheet("Sample Data");

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = { "STRING PARAMETER ID", "PNID", "VALUEDB", "VALUEPD", "SOURCE", "TENANT" };
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(getHeaderCellStyle(workbook));
            }

            int rowNum = 1;
            for (ReportDto repDTO : reportDto) {
                Row row = sheet.createRow(rowNum++);
                Cell cell = row.createCell(0);
                cell.setCellValue(repDTO.getStringParamId());

                Cell cell2 = row.createCell(1);
                cell2.setCellValue(repDTO.getPnid());

                Cell cell3 = row.createCell(2);
                cell3.setCellValue(repDTO.getValueInDB());

                Cell cell4 = row.createCell(3);
                cell4.setCellValue(repDTO.getValueInPD());

                Cell cell5 = row.createCell(4);
                cell5.setCellValue(repDTO.getSourceOfTruth());

                Cell cell6 = row.createCell(5);
                cell6.setCellValue(repDTO.getSyncTo());
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write workbook to a byte array
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);

            // Return the response
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=sample-data.xlsx")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(out.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("Error while creating Excel file", e);
        }
    }

    private CellStyle getHeaderCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    private String convertPVFromDBtoPDFormat(String parameterValue) {
        String paramVal = "";
        if (parameterValue.equals("Unbracketed Format")) {
            paramVal = THREE;
        } else if (parameterValue.contains(DAYS)) {
            paramVal = parameterValue.replace(" ", "");
        } else if (parameterValue.equals(ILMD1GS)) {
            paramVal = ILMD1;
        } else if (parameterValue.equals(ILMD2CB)) {
            paramVal = ILMD2;
        } else if (parameterValue.equals(CERTIFICATE)) {
            paramVal = CERT;
        } else if (parameterValue.equals(ASYNCHRONOUS)) {
            paramVal = ASYNC;
        } else if (parameterValue.equals(SYNCHRONOUS)) {
            paramVal = SYNC;
        } else if (parameterValue.contains(ATTP)) {
            paramVal = parameterValue.replace(" ", "");
        } else {
            ParameterValueUItoPDMapping parMapping = parameterValueUItoPDMappingRepository
                    .findTopByParameterValueUI(parameterValue);
            if (parMapping != null) {
                paramVal = parMapping.getParameterValuePD();
            } else {
                paramVal = parameterValue;
            }
        }
        return paramVal;
    }

    private ReportDto buildReportDto(String stringParamID, String pnid, String valueIndb, String valueInPD,
            String source, String syncTo) {
        return ReportDto.builder()
                .stringParamId(stringParamID)
                .pnid(pnid)
                .valueInDB(valueIndb)
                .valueInPD(valueInPD)
                .sourceOfTruth(source)
                .syncTo(syncTo)
                .build();
    }

    private List<String> getValueInPDWithMultiSelect(String pnid, String cpiStringParameter, String syncTo)
            throws JSONException, ParseException, IOException {

        String destination = (syncTo.equals("eu")) ? "CPI-Tenant-API" : "VRS_CPI-Tenant-API";
        try(CloseableHttpResponse getResponse = (CloseableHttpResponse)getCpiStringParameters(pnid, cpiStringParameter, destination)) {
            if (getResponse != null) {
                logger.info("getCpiStringParameters = " + getResponse.toString());
            }
            JSONObject responseObject;
            String currentPDValue = null;

            if (getResponse != null && getResponse.getStatusLine().getStatusCode() == 200) {
                responseObject = new JSONObject(EntityUtils.toString(getResponse.getEntity()));
                logger.info("responseObject = " + responseObject);
                if (responseObject.has("d")) {
                    JSONObject jsonObj = (JSONObject) responseObject.get("d");
                    currentPDValue = jsonObj.get("Value").toString();
                }
            } else {
                return null;
            }

            logger.info("current pd = " + currentPDValue);

            List<String> paramValues = Stream.of(currentPDValue.split(",", -1)).collect(Collectors.toList());
            logger.info("Customization value for Multiselect = " + paramValues);
            return paramValues;
        }
    }

    private List<String> getListOfValuesInDB(Customization customization) {

        List<String> valueInDBForMultiSelect = new ArrayList<>();
        String valueIndb = null;

        if (customization.getParameterKeyParameterKeyId() != 70) {
            valueIndb = customization.getMultipleParameterValuesString();
            List<String> paramValueIds = Stream.of(valueIndb.split(",", -1)).collect(Collectors.toList());
            List<String> getParamValues = new ArrayList<>();
            for (String paramValueId : paramValueIds) {
                ParameterValue pv = parameterValueRepository.findTopByParameterValueIdAndParameterOptionId(
                        Integer.valueOf(paramValueId), customization.getParameterOptionId());
                getParamValues.add(convertPVFromDBtoPDFormat(pv.getParameterValue()));
            }
            logger.info("Customization value for Multiselect = " + getParamValues);
            return getParamValues;
        } else {
            List<String> paramValues = Stream.of(valueIndb.split(",", -1)).collect(Collectors.toList());
            logger.info("Customization value for Multiselect with id 70 = " + paramValues);
            return paramValues;
        }

    }

    private String getValueInPD(String pnid, String cpiStringParameter, String syncTo)
            throws JSONException, ParseException, IOException {
        String destination = (syncTo.equals("eu")) ? "CPI-Tenant-API" : "VRS_CPI-Tenant-API";
        try(CloseableHttpResponse getResponse = (CloseableHttpResponse)getCpiStringParameters(pnid, cpiStringParameter, destination)) {
            if (getResponse != null) {
                logger.info("getCpiStringParameters = " + getResponse.toString());
            }
            JSONObject responseObject;
            String currentPDValue = null;

            if (getResponse != null && getResponse.getStatusLine().getStatusCode() == 200) {
                responseObject = new JSONObject(EntityUtils.toString(getResponse.getEntity()));
                logger.info("responseObject = " + responseObject);
                if (responseObject.has("d")) {
                    JSONObject jsonObj = (JSONObject) responseObject.get("d");
                    currentPDValue = jsonObj.get("Value").toString();
                }
            } else {
                return null;
            }

            logger.info("current pd = " + currentPDValue);
            return currentPDValue;
        }
    }

    private HttpResponse getCpiStringParameters(String pid, String id, String destination) {
        logger.info("-------------Inside getCpiStringParameters method------------");
        try {

            logger.info("Get string parameter Destination for id = {} is = {} ", id, destination);
            String endpoint = "/StringParameters";
            return iflowService.getStringParameters(destination, endpoint, pid, id);
        } catch (IOException e) {
            logger.error("get Partner Directory failed! e = {}", e.getMessage());
            throw new ResponseStatusException(HttpStatus.SC_INTERNAL_SERVER_ERROR, MessageKeys.PD_CREATION_FAILED, e);
        }
    }

    private String getValueForParameterOptionAndPnid(Customization customization) {

        String value = null;
        value = customization.getParameterValueText();
        if (value == null || value.isEmpty()) {
            ParameterValue paramValue = null;
            try {
                String multipleParamValuesString = customization.getMultipleParameterValuesString();
                if (multipleParamValuesString != null) {
                    paramValue = parameterValueRepository.findTopByParameterValueIdAndParameterOptionId(
                            Integer.valueOf(multipleParamValuesString),
                            customization.getParameterOptionId());
                }
            } catch (NumberFormatException e) {
                logger.error("Cannot parse null string in getValueForParameterOptionAndPnid", e);
            }
            value = paramValue != null ? paramValue.getParameterValue() : "";
        } else {
            if(customization.getAliasName() != null) {
                value = customization.getAliasName();
            }
        }

        return value;
    }
}
