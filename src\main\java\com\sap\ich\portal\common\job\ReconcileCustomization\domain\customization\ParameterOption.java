package com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "ICH_MANAGE_CUSTOMIZATION_PARAMETEROPTION")
@SuppressWarnings("unused")
public class ParameterOption implements Serializable {

    static final long serialVersionUID = 1L;

    @Id
    @Column(name = "PARAMETEROPTIONID")
    private Integer parameterOptionId;

    @Column(name = "CPISTRINGPARAMETER")
    private String cpiStringParameter;
    
    @Column(name = "SYNCTO")
     private String syncTo;    

}
