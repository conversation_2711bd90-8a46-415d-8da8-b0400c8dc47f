import org.jenkinsci.plugins.pipeline.modeldefinition.Utils
void call(Map params) {
    //access stage name
    echo "Start - Extension for stage: ${params.stageName}"
    //access config
    echo "Current stage config: ${params.config}"

    def CF_REGION, CF_ORG, CF_SPACE, CF_SUBDOMAIN, CF_DT, CF_MTAEXT, CF_MANIFEST, CF_VARS
    CF_DT='standard'

    if(env.BRANCH_NAME.startsWith('release') || env.BRANCH_NAME.startsWith('hotfix')){
        echo "${env.BRANCH_NAME} branch detected! ----> Deploying to QA"
        params.originalStage()
    }else{
        echo "Develop branch detected! ----> Deploying to selected env: ${env.DEPLOY_TO}"
        switch(env.DEPLOY_TO){
            case 'Develop':
                CF_REGION = 'eu12'
                CF_ORG = 'ich-dev-org'
                CF_SPACE = 'portal'
                CF_MANIFEST = 'manifest-dev.yaml'
                CF_VARS = ['vars-dev.yml']
                break
            case 'QA':
                CF_REGION = 'eu10-004'
                CF_ORG = 'ich-qa-new-org'
                CF_SPACE = 'portal'
                CF_MANIFEST = 'manifest-qa.yaml'
                CF_VARS = ['vars-qa.yml']
                break
            case 'Dev Validation':
                CF_REGION = 'eu12'
                CF_ORG = 'ich-dev-validation'
                CF_SPACE = 'portal'
                CF_MANIFEST = 'manifest-devGxP.yaml'
                CF_VARS = ['vars-devGxP.yml']
                break
            case 'QA Validation':
                CF_REGION = 'eu10-004'
                CF_ORG = 'ich-qa-validation'
                CF_SPACE = 'portal'
                CF_MANIFEST = 'manifest-qaGxP.yaml'
                CF_VARS = ['vars-qaGxP.yml']
                break
            default:
                break
        }

        try{
            downloadArtifactsFromNexus script: this, fromStaging: true
            cloudFoundryDeploy script: this, cfCredentialsId: 'ICH_pwd_Cloud_Platform', space: CF_SPACE, org: CF_ORG, cfManifest: CF_MANIFEST, cfManifestVariablesFiles: CF_VARS, apiEndpoint: "https://api.cf.${CF_REGION}.hana.ondemand.com", mtaDeployParameters: "-f --version-rule ALL"
        }
        catch(Throwable error){
            globalPipelineEnvironment.addError(this, err)
            throw err
        }
    }
    
    echo "End - Extension for stage: ${params.stageName}"
}
return this
