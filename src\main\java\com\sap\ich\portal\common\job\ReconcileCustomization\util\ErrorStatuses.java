package com.sap.ich.portal.common.job.ReconcileCustomization.util;

public enum ErrorStatuses {

    // DEFAULT HTTP ERROR CODES
    BAD_REQUEST(400, "Bad request", 400),

    UNAUTHORIZED(401, "No authentication", 401), // == unauthenticated (misleading http name)

    FORBIDDEN(403, "No authorization", 403), // == unauthorized

    NOT_FOUND(404, "Not found", 404),

    METHOD_NOT_ALLOWED(405, "Method not allowed", 405),

    NOT_ACCEPTABLE(406, "Not acceptable", 406),

    CONFLICT(409, "Conflict", 409),

    GONE(410, "Gone", 410),

    PRECONDITION_FAILED(412, "Precondition failed", 412),

    UNSUPPORTED_MEDIA_TYPE(415, "Unsupported media type", 415),

    MISDIRECTED_REQUEST(421, "Misdirected request", 421),

    UNPROCESSABLE_ENTITY(422, "Unprocessable entity", 422),

    LOCKED(423, "Locked", 423),

    PRECONDITION_REQUIRED(428, "Precondition required", 428),

    SERVER_ERROR(500, "Internal server error", 500),

    NOT_IMPLEMENTED(501, "Not implemented", 501),

    BAD_GATEWAY(502, "Bad gateway", 502),

    GATEWAY_TIMEOUT(504, "Gateway timeout", 504);

    private final int code;
    private final String description;
    private final int httpStatus;

    ErrorStatuses(int code, String description, int httpStatus) {
        this.code = code;
        this.description = description;
        this.httpStatus = httpStatus;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public int getHttpStatus() {
        return httpStatus;
    }
}
