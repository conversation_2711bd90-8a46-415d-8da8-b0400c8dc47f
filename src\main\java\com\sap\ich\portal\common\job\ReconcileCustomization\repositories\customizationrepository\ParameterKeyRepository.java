package com.sap.ich.portal.common.job.ReconcileCustomization.repositories.customizationrepository;

import org.springframework.data.jpa.repository.JpaRepository;
import com.sap.ich.portal.common.job.ReconcileCustomization.domain.customization.ParameterKey;

public interface ParameterKeyRepository extends JpaRepository<ParameterKey, String> {
    
    ParameterKey findTopByParameterKeyId(Integer parameterKeyId);
}
